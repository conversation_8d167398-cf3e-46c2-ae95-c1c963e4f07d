import { Component, EventEmitter, Input, Output, signal, TemplateRef, ChangeDetectionStrategy, ViewChild, ViewEncapsulation, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ListConfig, SortState } from '../../models/view/list-layout.model';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { Swiper } from 'swiper';
import { getElementMaxHeightToFit100vh } from '@/shared/utils';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ListColumnSelectorModalService } from '@/shared/modals/common/list-column-selector-modal/list-column-selector-modal.service';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { mockFields } from '@/mock/fields.mock';
import { FieldFiltersComponent } from '../field-filters/field-filters.component';
import { FieldFilter, FilterChangeEvent } from '../field-filters/models/view/field-filter-view.model';
import { ResizePanelDirective } from '@/shared/directives/resize-panel/resize-panel.directive';
import { trigger, state, style, transition, animate, AnimationEvent } from '@angular/animations';

@Component({
  selector: 'app-list-layout',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    MatTooltipModule,
    FieldFiltersComponent,
    ResizePanelDirective
  ],
  templateUrl: './list-layout.component.html',
  styleUrls: ['./list-layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  animations: [
    trigger('slideFilter', [
      state('hidden', style({
        transform: 'translateY(-100%)',
        opacity: 0
      })),
      state('visible', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('hidden => visible', [
        animate('300ms ease-out')
      ]),
      transition('visible => hidden', [
        animate('300ms ease-in')
      ])
    ])
  ]
})
export class ListLayoutComponent {
  @Input() config!: ListConfig;
  @Input() fixedColumnTemplate?: TemplateRef<any>;
  @Input() scrollableColumnsTemplate?: TemplateRef<any>;
  @Input() actionsTemplate?: TemplateRef<any>;
  @Output() filterChange = new EventEmitter<Record<string, any>>();
  @Output() sortChange = new EventEmitter<SortState>();
  @Output() onActionClick = new EventEmitter<{ item: any; actionType: string; extra?: any }>();
  @Output() columnsChange = new EventEmitter<{ visibleColumns: string[]; columnOrder: string[] }>();
  @Output() pageChange = new EventEmitter<{ page: number; pageSize: number }>();

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild('filter', { static: true }) filter!: ElementRef<HTMLElement>;
  @ViewChild('list', { static: true }) list!: ElementRef<HTMLElement>;

  @ViewChild('fixedColumns', { static: true }) fixedColumns!: ElementRef<any>;
  @ViewChild('scrollableContainer', { static: true }) scrollableContainer!: ElementRef<any>;
  @ViewChild('scrollableSection', { static: true }) scrollableSection!: ElementRef<any>;
  @ViewChild('scrollableHeader', { static: true }) scrollableHeader!: ElementRef<any>;
  @ViewChild('scrollableScrollbar', { static: true }) scrollableScrollbar!: ElementRef<any>;
  @ViewChild('listContainer', { static: true }) listContainer!: ElementRef<any>;
  @ViewChild('paginatorEle', { static: true }) paginatorEle!: ElementRef<any>;

  visibleColumns = signal<string[]>([]);
  filterValues = signal<Record<string, any>>({});
  sortState = signal<SortState>({ key: '', direction: '' });
  columnOrder = signal<string[]>([]);
  pageSizeOptions = [2, 5, 10, 20];
  hoverState = signal<Record<string, boolean>>({});

  // Filter visibility state management
  isFilterVisible = signal<boolean>(false);
  isFilterRendered = signal<boolean>(false);
  filterAnimationState = signal<'hidden' | 'visible'>('hidden');

  mockFields = mockFields;


  private swiperInstance?: Swiper;

  constructor(
    private listColumnSelectorModalService: ListColumnSelectorModalService
  ) {

  }
  ngOnInit() {
    // Khởi tạo visibleColumns và columnOrder, loại bỏ fixedColumns
    const nonFixedColumns = this.config.columns.filter(col => !this.config.fixedColumns.some(fc => fc.key === col.key));
    this.visibleColumns.set(nonFixedColumns.map(col => col.key));
    this.columnOrder.set(this.config.columnOrder || nonFixedColumns.map(col => col.key));

    // this.editColumns()
  }

  ngAfterViewInit() {
    // Đồng bộ trạng thái MatPaginator
    if (this.config.pager) {
      this.paginator.pageIndex = this.config.pager.currentPage - 1;
      this.paginator.pageSize = this.config.pager.pageSize;
      this.paginator.length = this.config.pager.totalPages * this.config.pager.pageSize;
    }

    this.initSwiper();
  }

  initSwiper() {
    if(!this.swiperInstance &&
      this.listContainer?.nativeElement &&
      this.scrollableContainer?.nativeElement &&
      this.fixedColumns?.nativeElement
    ) {
      const paginatorHeight = (this.paginatorEle?.nativeElement?.offsetHeight || 0) + 20;
      const maxHeight = getElementMaxHeightToFit100vh(this.listContainer.nativeElement, 10);
      this.listContainer.nativeElement.style.setProperty('max-height', `${maxHeight}px`);

      const fixedColumnWidth = this.fixedColumns?.nativeElement?.offsetWidth || 0;
      this.scrollableContainer.nativeElement!.style.width = `calc(100% - ${fixedColumnWidth}px)`;



      this.swiperInstance = new Swiper(this.scrollableSection.nativeElement, {
        slidesPerView: 'auto',
        freeMode: {
          enabled: true,
          momentumRatio: 1, // Đảm bảo animation mượt, không chậm
        },
        scrollbar: {
          enabled: true,
          el: this.scrollableScrollbar?.nativeElement,
          hide: false,
          draggable: true,
          snapOnRelease: true,
        },
        // mousewheel: {
        //   enabled: true,
        //   forceToAxis: true,
        //   sensitivity: 1,
        //   releaseOnEdges: true
        // },
        // resistanceRatio: 0.5,
        // touchRatio: 1,
        // simulateTouch: true
      });


      this.swiperInstance.on('setTranslate', () => {
        if (this.scrollableHeader?.nativeElement && this.swiperInstance) {
          requestAnimationFrame(() => {
            this.scrollableHeader.nativeElement.style.transform = `translateX(${this.swiperInstance!.translate}px)`;
          });
        }
      });
    }
  }

  trackById(index: number, item: any): string {
    return item.id;
  }

  /**
   * Toggle filter visibility với animation slide up/down
   * Sequence cho SHOWING: render component -> animate slide down
   * Sequence cho HIDING: animate slide up -> remove component
   */
  toggleFilter() {
    const currentlyVisible = this.isFilterVisible();

    if (!currentlyVisible) {
      // SHOWING sequence: render first, then animate
      this.showFilter();
    } else {
      // HIDING sequence: animate first, then remove
      this.hideFilter();
    }
  }

  /**
   * Show filter với animation sequence
   */
  private showFilter(): void {
    // Step 1: Render component (set ngIf to true)
    this.isFilterRendered.set(true);
    this.isFilterVisible.set(true);

    // Step 2: Trigger slide-down animation after render
    setTimeout(() => {
      this.filterAnimationState.set('visible');
    }, 10); // Small delay để đảm bảo DOM đã render
  }

  /**
   * Hide filter với animation sequence
   */
  private hideFilter(): void {
    // Step 1: Trigger slide-up animation
    this.filterAnimationState.set('hidden');
    this.isFilterVisible.set(false);

    // Step 2: Remove component after animation completes (300ms)
    setTimeout(() => {
      this.isFilterRendered.set(false);
    }, 300);
  }

  /**
   * Handle animation events để đảm bảo proper timing
   */
  onFilterAnimationDone(event: AnimationEvent): void {
    if (event.toState === 'hidden') {
      // Animation hide hoàn tất, có thể remove khỏi DOM
      this.isFilterRendered.set(false);
    }
  }

  onFilterChange(key: string, value: any) {
    this.filterValues.set({ ...this.filterValues(), [key]: value });
    this.filterChange.emit(this.filterValues());
  }

  sortColumn(key: string) {
    const column = this.config.allColumns.find(c => c.key === key) || this.config.fixedColumns.find(c => c.key === key);
    if (!column?.sortable) return;
    const currentSort = this.sortState();
    let direction: 'asc' | 'desc' | '' = '';
    if (currentSort.key === key) {
      direction = currentSort.direction === 'asc' ? 'desc' : currentSort.direction === 'desc' ? '' : 'asc';
    } else {
      direction = 'asc';
    }
    this.sortState.set({ key, direction });
    this.sortChange.emit({ key, direction });
  }

  handleActionClick(item: any, actionType: string, extra?: any) {
    this.onActionClick.emit({ item, actionType, extra });
  }

  onColumnsChange({ visibleColumns, columnOrder }: { visibleColumns: string[]; columnOrder: string[] }) {
    this.visibleColumns.set(visibleColumns);
    this.columnOrder.set(columnOrder);
    this.columnsChange.emit({ visibleColumns, columnOrder });
  }

  getColumnLabel(key: string): string {
    return (
      this.config.allColumns.find(c => c.key === key)?.label ||
      this.config.fixedColumns.find(c => c.key === key)?.label ||
      ''
    );
  }

  onPageChange(event: { pageIndex: number; pageSize: number }) {
    const page = event.pageIndex + 1;
    const pageSize = event.pageSize;
    this.pageChange.emit({ page, pageSize });
  }

  onRowHover(item: any) {
    this.hoverState.set({ ...this.hoverState(), [item.id]: true });
  }

  onRowLeave(item: any) {
    this.hoverState.set({ ...this.hoverState(), [item.id]: false });
  }

  editColumns() {
    this.listColumnSelectorModalService.open({
      allColumns: this.config.allColumns,
      regularColumns: this.visibleColumns(),
      pinnedColumns: [],
      maxPinnedColumns: 3
    });
  }

  /**
   * Xử lý khi filter thay đổi
   * @param event - Filter change event
   */
  onFieldFilterChange(event: FilterChangeEvent): void {
    // console.log('Field filter changed:', event);

    // // Tìm field tương ứng
    // const field = this.mockFields.find(f => f._id === event.fieldId);
    // if (field) {
    //   const status = event.isActive ? 'activated' : 'deactivated';
    //   console.log(`Filter for "${field.label}" ${status}`);

    //   if (event.isActive && event.filterValue) {
    //     console.log('Filter value:', event.filterValue);
    //   }
    // }
  }

  /**
   * Xử lý khi các filters được áp dụng
   * @param activeFilters - Danh sách các filter đang active
   */
  onFiltersApplied(activeFilters: FieldFilter[]): void {
    console.log('Active filters applied:', activeFilters);

    if (activeFilters.length > 0) {
      const filterSummary = activeFilters.map(filter => {
        const operator = filter.filterValue?.operator || 'unknown';
        const value = this.getFilterValueDisplay(filter.filterValue);
        return `${filter.field.label}: ${operator}${value ? ` (${value})` : ''}`;
      }).join(', ');

      console.log(`Applied ${activeFilters.length} filter(s): ${filterSummary}`);
    } else {
      console.log('No filters applied');
    }
  }

  /**
   * Xử lý khi filters được reset
   */
  onFiltersReset(): void {
    console.log('Filters have been reset');
  }

  /**
   * Helper method để hiển thị giá trị filter
   * @param filterValue - Giá trị filter
   * @returns String representation của giá trị
   */
  private getFilterValueDisplay(filterValue: any): string {
    if (!filterValue) return '';

    if (filterValue.value !== undefined) {
      return String(filterValue.value);
    }

    if (filterValue.values && Array.isArray(filterValue.values)) {
      return filterValue.values.join(', ');
    }

    if (filterValue.minValue !== undefined && filterValue.maxValue !== undefined) {
      return `${filterValue.minValue} - ${filterValue.maxValue}`;
    }

    if (filterValue.timeValue !== undefined && filterValue.timeUnit) {
      return `${filterValue.timeValue} ${filterValue.timeUnit}`;
    }

    return '';
  }
}
