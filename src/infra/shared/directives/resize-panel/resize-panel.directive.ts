import { Directive, ElementRef, Input, HostListener, On<PERSON>nit, OnD<PERSON>roy, inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

// Dịch vụ lưu trữ localStorage
@Injectable({ providedIn: 'root' })
export class StorageService {
  setItem(key: string, value: string): void {
    localStorage.setItem(key, value);
  }

  getItem(key: string): string | null {
    return localStorage.getItem(key);
  }
}

@Directive({
  selector: '[appResizePanel]',
  standalone: true
})
export class ResizePanelDirective implements OnInit, OnDestroy {
  private el = inject(ElementRef);
  private translateService = inject(TranslateService);
  private storageService = inject(StorageService);

  // Inputs
  @Input() leftPanel!: ElementRef<HTMLElement> | HTMLElement;
  @Input() rightPanel!: ElementRef<HTMLElement> | HTMLElement;
  @Input() minWidth: number = 200;
  @Input() maxWidth: number = 600;
  @Input() isAbsolute: boolean = false;
  @Input() panelName: string = ''; // Thêm input panelName để định danh panel

  private panelWidthSubject = new BehaviorSubject<number>(this.minWidth);
  panelWidth$: Observable<number> = this.panelWidthSubject.asObservable();

  private get storageKey(): string {
    return this.panelName ? `panel_width_${this.panelName}` : 'panel_width'; // Tạo key duy nhất dựa trên panelName
  }

  private isDragging = false;
  private isHidden = false; // Track panel visibility state
  private savedStyles: { marginLeft: string; width: string } | null = null; // Store original styles

  ngOnInit(): void {
    // Khởi tạo chiều rộng từ localStorage hoặc minWidth
    const savedWidth = this.storageService.getItem(this.storageKey);
    const initialWidth = savedWidth ? parseInt(savedWidth, 10) : this.minWidth;
    this.setPanelWidth(initialWidth);

    // Thêm class CSS cho resize handle
    this.el.nativeElement.classList.add('resize-handle');

    // Thêm tooltip dịch từ i18n
    this.translateService.get('RESIZE_PANEL.TOOLTIP').subscribe(tooltip => {
      this.el.nativeElement.setAttribute('title', tooltip);
    });
  }

  @HostListener('mousedown', ['$event'])
  onMouseDown(event: MouseEvent): void {
    this.isDragging = true;
    event.preventDefault();
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    if (!this.isDragging) return;

    const newWidth = event.clientX;
    if (newWidth >= this.minWidth && newWidth <= this.maxWidth) {
      this.setPanelWidth(newWidth);
    }
  }

  @HostListener('document:mouseup')
  onMouseUp(): void {
    this.isDragging = false;
  }

  private setPanelWidth(width: number): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    // Cập nhật style dựa trên isAbsolute
    if (this.isAbsolute) {
      // Bố cục absolute
      leftPanelEl.style.width = `${width}px`;
      leftPanelEl.style.position = 'absolute';
      leftPanelEl.style.top = '0';
      leftPanelEl.style.left = '0';
      rightPanelEl.style.marginLeft = `${width + 10}px`;
      rightPanelEl.style.width = `calc(100% - ${width + 10}px)`;
    } else {
      // Bố cục flex
      leftPanelEl.style.width = `${width}px`;
      leftPanelEl.style.position = ''; // Xóa position nếu có
      rightPanelEl.style.marginLeft = ''; // Xóa margin-left
      rightPanelEl.style.width = ''; // Xóa width, để flex-grow xử lý
    }

    // Lưu chiều rộng vào localStorage và cập nhật BehaviorSubject
    this.panelWidthSubject.next(width);
    this.storageService.setItem(this.storageKey, width.toString());
  }

  /**
   * Hide panel và reset styles của adjacent panel về full width
   * Method này được gọi từ component khi cần ẩn panel
   */
  hide(): void {
    if (this.isHidden) return; // Đã ẩn rồi

    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    // Lưu styles hiện tại trước khi reset
    this.savedStyles = {
      marginLeft: rightPanelEl.style.marginLeft || '',
      width: rightPanelEl.style.width || ''
    };

    // Reset adjacent panel về full width
    rightPanelEl.style.marginLeft = '0';
    rightPanelEl.style.width = '100%';

    this.isHidden = true;
  }

  /**
   * Show panel và restore styles của adjacent panel
   * Method này được gọi từ component khi cần hiện panel
   */
  show(): void {
    if (!this.isHidden) return; // Đã hiện rồi

    // Restore lại panel width từ localStorage hoặc minWidth
    const savedWidth = this.storageService.getItem(this.storageKey);
    const currentWidth = savedWidth ? parseInt(savedWidth, 10) : this.minWidth;

    // Apply lại styles cho cả 2 panels
    this.setPanelWidth(currentWidth);

    this.isHidden = false;
    this.savedStyles = null; // Clear saved styles
  }

  /**
   * Check if panel is currently hidden
   */
  isCurrentlyHidden(): boolean {
    return this.isHidden;
  }

  ngOnDestroy(): void {
    this.panelWidthSubject.complete();
  }
}
