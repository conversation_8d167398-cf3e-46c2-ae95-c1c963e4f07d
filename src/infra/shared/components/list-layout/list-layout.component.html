<div class="list-layout">
  <!-- <div>
    Product List
    <i class="fa-solid fa-circle-info"></i>
  </div> -->

  <!-- Thanh filter -->
  <div class="list-layout-header flex flex-nowrap align-items-center">
    <div class="flex align-items-center">
      <div
        class="button-24 flex align-items-center"
        [class.active]="isFilterVisible()"
        matTooltip="Filter"
        (click)="toggleFilter()"
        >
        <div class="button__icon">
          <i class="fa-light fa-filter-list"></i>
        </div>
      </div>

      <div
        class="button-24 flex align-items-center"
        matTooltip="Edit Columns"
        (click)="editColumns()"
        >
        <div class="button__icon">
          <i class="fa-light fa-objects-column"></i>
        </div>
      </div>

      <div
        class="button-24 flex align-items-center"
        matTooltip="Text size"
        >
        <div class="button__icon">
          <i class="fa-light fa-text-size"></i>
        </div>
      </div>

      <div class="page-list-info flex align-items-center">
        All Products

        <div class="button__icon ms-3">
          <i class="fa-solid fa-circle-info"></i>
        </div>
      </div>

      <!-- <div class="button-24 flex align-items-center">
        <div class="button__text pe-3">
          <i class="fa-solid fa-square-check"></i>
          <span class="mx-2">Đã chọn (5)</span>
          <i class="fa-regular fa-chevron-down"></i>
        </div>
      </div> -->
    </div>


    <div class="ms-auto">
     <!-- MatPaginator -->
      <div #paginatorEle>
      <mat-paginator
        *ngIf="config.pager"
        [pageIndex]="config.pager.currentPage - 1"
        [pageSize]="config.pager.pageSize"
        [length]="config.pager.totalPages * config.pager.pageSize"
        [pageSizeOptions]="pageSizeOptions"
        (page)="onPageChange($event)">
      </mat-paginator>
      </div>
    </div>
  </div>



  <!-- Danh sách -->
  <div class="list-container" #listContainer>
    <!-- Filter panel với animation -->
    <div
      *ngIf="isFilterRendered()"
      class="field-filters"
      #filter
      [@slideFilter]="filterAnimationState()"
      (@slideFilter.done)="onFilterAnimationDone($event)">
      <div class="relative h-100">
        <app-field-filters
          [fields]="mockFields"
          [showTitle]="true"
          [showClearAll]="true"
          (filterChange)="onFieldFilterChange($event)"
          (filtersApplied)="onFiltersApplied($event)"
          (filtersReset)="onFiltersReset()">
        </app-field-filters>

        <div
          appResizePanel
          class="panel-resize-handle resize-handle--absolute"
          [leftPanel]="filter"
          [rightPanel]="list"
          [minWidth]="300"
          [maxWidth]="600"
          [isAbsolute]="true"
          [panelName]="'list-layout'">
        </div>
      </div>
    </div>


    <div
      class="table-wrapper"
      #list
      [class.full-width]="!isFilterVisible()">
      <!-- Block trái: Fixed columns -->
      <div class="fixed-columns" #fixedColumns>
        <div class="fixed-header">
          <div *ngFor="let col of config.fixedColumns" class="fixed-column" (click)="sortColumn(col.key)">
            {{ col.label }}
            <span *ngIf="sortState().key === col.key && sortState().direction">
              {{ sortState().direction === 'asc' ? '↑' : '↓' }}
            </span>
          </div>
        </div>
        <div class="fixed-body">
          <div
            class="fixed-row"
            [class.hover]="hoverState()[item.id]"
            (mouseenter)="onRowHover(item)"
            (mouseleave)="onRowLeave(item)"
            *ngFor="let item of config.items; trackBy: trackById"
            >
            <ng-container *ngIf="fixedColumnTemplate; else defaultFixedColumns">
              <ng-container *ngTemplateOutlet="fixedColumnTemplate; context: { $implicit: item, fixedColumns: config.fixedColumns }"></ng-container>
            </ng-container>
            <ng-template #defaultFixedColumns>
              <div *ngFor="let col of config.fixedColumns" class="fixed-column">
                {{ item[col.key] }}
              </div>
            </ng-template>
          </div>
        </div>
      </div>

      <!-- Block phải: Scrollable columns + Actions -->
      <div class="scrollable-container" #scrollableContainer>
        <!-- Header sticky -->
        <div class="scrollable-header" #scrollableHeader>
          <div *ngFor="let col of columnOrder()" class="column-header" (click)="sortColumn(col)">
            {{ getColumnLabel(col) }}
            <span *ngIf="sortState().key === col && sortState().direction">
              {{ sortState().direction === 'asc' ? '↑' : '↓' }}
            </span>
          </div>
          <div class="column-header actions-column">Hành động</div>
        </div>
        <!-- Body cuộn ngang bằng Swiper -->
        <div class="scrollable-section swiper" #scrollableSection>
          <div class="swiper-wrapper">
            <div class="scrollable-table swiper-slide">
              <div class="scrollable-body">
                <div
                  class="scrollable-row"
                  [class.hover]="hoverState()[item.id]"
                  (mouseenter)="onRowHover(item)"
                  (mouseleave)="onRowLeave(item)"
                  *ngFor="let item of config.items; trackBy: trackById">
                  <ng-container *ngIf="scrollableColumnsTemplate; else defaultScrollableColumns">
                    <ng-container *ngTemplateOutlet="scrollableColumnsTemplate; context: { $implicit: item, visibleColumns: columnOrder() }"></ng-container>
                  </ng-container>
                  <ng-template #defaultScrollableColumns>
                    <div *ngFor="let col of columnOrder()" class="column-data">
                      {{ item[col] }}
                    </div>
                  </ng-template>

                  <div class="column-data actions-column">Hành động</div>


                  <ng-container *ngIf="actionsTemplate; else defaultActions">
                    <ng-container *ngTemplateOutlet="actionsTemplate; context: { $implicit: item, onActionClick: handleActionClick.bind(this) }"></ng-container>
                  </ng-container>
                  <ng-template #defaultActions>
                    <div class="actions-column">
                      <button (click)="handleActionClick(item, 'view')">Xem chi tiết</button>
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>
          </div>

          <div class="swiper-scrollbar" #scrollableScrollbar></div>
        </div>
      </div>
    </div>
  </div>
</div>
