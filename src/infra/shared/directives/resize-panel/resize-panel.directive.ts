import { Directive, ElementRef, Input, HostListener, On<PERSON>nit, OnD<PERSON>roy, inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

// Dịch vụ lưu trữ localStorage
@Injectable({ providedIn: 'root' })
export class StorageService {
  setItem(key: string, value: string): void {
    localStorage.setItem(key, value);
  }

  getItem(key: string): string | null {
    return localStorage.getItem(key);
  }
}

@Directive({
  selector: '[appResizePanel]',
  standalone: true
})
export class ResizePanelDirective implements OnInit, OnDestroy {
  private el = inject(ElementRef);
  private translateService = inject(TranslateService);
  private storageService = inject(StorageService);

  // Inputs
  @Input() leftPanel!: ElementRef<HTMLElement> | HTMLElement;
  @Input() rightPanel!: ElementRef<HTMLElement> | HTMLElement;
  @Input() minWidth: number = 200;
  @Input() maxWidth: number = 600;
  @Input() isAbsolute: boolean = false;
  @Input() panelName: string = ''; // Thêm input panelName để định danh panel

  private panelWidthSubject = new BehaviorSubject<number>(this.minWidth);
  panelWidth$: Observable<number> = this.panelWidthSubject.asObservable();

  private get storageKey(): string {
    return this.panelName ? `panel_width_${this.panelName}` : 'panel_width'; // Tạo key duy nhất dựa trên panelName
  }

  private isDragging = false;
  private isHidden = false; // Track panel visibility state
  private savedStyles: { marginLeft: string; width: string } | null = null; // Store original styles
  private isAnimating = false; // Track animation state
  private animationDuration = 300; // Animation duration in ms

  ngOnInit(): void {
    // Khởi tạo chiều rộng từ localStorage hoặc minWidth
    const savedWidth = this.storageService.getItem(this.storageKey);
    const initialWidth = savedWidth ? parseInt(savedWidth, 10) : this.minWidth;
    this.setPanelWidth(initialWidth);

    // Thêm class CSS cho resize handle
    this.el.nativeElement.classList.add('resize-handle');

    // Setup CSS transitions cho animation
    this.setupAnimationStyles();

    // Thêm tooltip dịch từ i18n
    this.translateService.get('RESIZE_PANEL.TOOLTIP').subscribe(tooltip => {
      this.el.nativeElement.setAttribute('title', tooltip);
    });
  }

  @HostListener('mousedown', ['$event'])
  onMouseDown(event: MouseEvent): void {
    this.isDragging = true;
    // Disable transitions during drag for smooth performance
    this.disableTransitions();
    event.preventDefault();
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    if (!this.isDragging) return;

    const newWidth = event.clientX;
    if (newWidth >= this.minWidth && newWidth <= this.maxWidth) {
      this.setPanelWidth(newWidth);
    }
  }

  @HostListener('document:mouseup')
  onMouseUp(): void {
    if (this.isDragging) {
      this.isDragging = false;
      // Re-enable transitions after drag
      this.enableTransitions();
    }
  }

  /**
   * Setup CSS transitions cho smooth animation
   */
  private setupAnimationStyles(): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    // Apply CSS transitions cho smooth animation
    const transitionStyle = `width ${this.animationDuration}ms ease-out, margin-left ${this.animationDuration}ms ease-out, opacity ${this.animationDuration}ms ease-out`;

    leftPanelEl.style.transition = transitionStyle;
    rightPanelEl.style.transition = transitionStyle;

    // Đảm bảo overflow hidden để tránh content bleeding
    leftPanelEl.style.overflow = 'hidden';
  }

  private setPanelWidth(width: number): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    // Cập nhật style dựa trên isAbsolute
    if (this.isAbsolute) {
      // Bố cục absolute
      leftPanelEl.style.width = `${width}px`;
      leftPanelEl.style.position = 'absolute';
      leftPanelEl.style.top = '0';
      leftPanelEl.style.left = '0';
      rightPanelEl.style.marginLeft = `${width + 10}px`;
      rightPanelEl.style.width = `calc(100% - ${width + 10}px)`;
    } else {
      // Bố cục flex
      leftPanelEl.style.width = `${width}px`;
      leftPanelEl.style.position = ''; // Xóa position nếu có
      rightPanelEl.style.marginLeft = ''; // Xóa margin-left
      rightPanelEl.style.width = ''; // Xóa width, để flex-grow xử lý
    }

    // Lưu chiều rộng vào localStorage và cập nhật BehaviorSubject
    this.panelWidthSubject.next(width);
    this.storageService.setItem(this.storageKey, width.toString());
  }

  /**
   * Hide panel với CSS animation
   * Method này được gọi từ component khi cần ẩn panel
   */
  hide(): Promise<void> {
    if (this.isHidden || this.isAnimating) return Promise.resolve(); // Đã ẩn rồi hoặc đang animate

    return new Promise((resolve) => {
      this.isAnimating = true;

      const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
      const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

      // Lưu styles hiện tại trước khi reset
      this.savedStyles = {
        marginLeft: rightPanelEl.style.marginLeft || '',
        width: rightPanelEl.style.width || ''
      };

      // Animate panel collapse
      leftPanelEl.style.width = '0px';
      leftPanelEl.style.opacity = '0';

      // Animate adjacent panel expansion
      rightPanelEl.style.marginLeft = '0';
      rightPanelEl.style.width = '100%';

      // Wait for animation to complete
      setTimeout(() => {
        this.isHidden = true;
        this.isAnimating = false;
        resolve();
      }, this.animationDuration);
    });
  }

  /**
   * Show panel với CSS animation
   * Method này được gọi từ component khi cần hiện panel
   */
  show(): Promise<void> {
    if (this.isAnimating) return Promise.resolve(); // Đang animate

    return new Promise((resolve) => {
      this.isAnimating = true;

      const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;

      // Restore lại panel width từ localStorage hoặc minWidth
      const savedWidth = this.storageService.getItem(this.storageKey);
      const currentWidth = savedWidth ? parseInt(savedWidth, 10) : this.minWidth;

      // Start animation by setting initial collapsed state
      leftPanelEl.style.width = '0px';
      leftPanelEl.style.opacity = '0';

      // Force reflow để đảm bảo initial state được apply
      leftPanelEl.offsetHeight;

      // Then animate to target values
      leftPanelEl.style.opacity = '1';
      this.setPanelWidth(currentWidth);

      // Wait for animation to complete
      setTimeout(() => {
        this.isHidden = false;
        this.isAnimating = false;
        this.savedStyles = null; // Clear saved styles
        resolve();
      }, this.animationDuration);
    });
  }

  /**
   * Disable transitions temporarily (useful for instant updates during resize)
   */
  private disableTransitions(): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    leftPanelEl.style.transition = 'none';
    rightPanelEl.style.transition = 'none';
  }

  /**
   * Re-enable transitions
   */
  private enableTransitions(): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    const rightPanelEl = this.rightPanel instanceof HTMLElement ? this.rightPanel : this.rightPanel.nativeElement;

    const transitionStyle = `width ${this.animationDuration}ms ease-out, margin-left ${this.animationDuration}ms ease-out, opacity ${this.animationDuration}ms ease-out`;
    leftPanelEl.style.transition = transitionStyle;
    rightPanelEl.style.transition = transitionStyle;
  }

  /**
   * Check if panel is currently hidden
   */
  isCurrentlyHidden(): boolean {
    return this.isHidden;
  }

  /**
   * Check if panel is currently animating
   */
  isCurrentlyAnimating(): boolean {
    return this.isAnimating;
  }

  ngOnDestroy(): void {
    this.panelWidthSubject.complete();
  }
}
