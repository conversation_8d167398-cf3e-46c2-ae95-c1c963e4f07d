$backgroundColor: #fafafa;

.list-layout {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.list-layout-header {
  // border-bottom: 1px solid #eee;
  // padding-bottom: 1em;
}

.list-container {
  position: relative;
  display: flex;
  width: 100%;
  gap: 0; // No gap để animation mượt hơn

  font-size: .95em;
  letter-spacing: -.01em;
  font-weight: 400;
  border-radius: 4px;
  min-height: 300px;
}

.field-filters {
  // Flexbox item thay vì absolute positioning
  flex-shrink: 0;
  height: 100%;

  // Animation properties được control bởi Angular Animation
  // width và minWidth sẽ được set bởi animation state

  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  padding: 1rem;
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;

  // Animation support cho horizontal resize
  will-change: width, min-width, opacity;

  background-color: white;
  z-index: 10;
}
.table-wrapper {
  // Flexbox item sẽ tự động fill remaining space
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
  overflow-y: auto;

  border: 1px solid #ddd;
  border-radius: 15px;

  // Không cần transition width vì animation được handle bởi filter panel
  // Table wrapper sẽ tự động resize theo flex layout

  // Không cần .full-width class nữa vì flexbox tự handle
}





.page-list-info,
.button-24 {
  background-color: #fff;
  border: 1px solid #d2d6e0;
  border-radius: 6px;
  box-shadow: rgba(37, 44, 97, 0.15) 0 4px 11px 0, rgba(93, 100, 148, 0.2) 0 1px 3px 0;
  color: #484c7a;

  font-size: .9em;
  font-weight: 500;
  letter-spacing: -0.02em;

  margin: 4px;
  text-align: center;
  text-decoration: inherit;
  text-wrap: nowrap;
  line-height: 1;
  height: 36px;
  overflow: hidden;
  touch-action: manipulation;
  width: auto;

}
.page-list-info {
  padding: 0 15px;
}


.button-24 {
  background-image: linear-gradient(#fff, #ebebeb);
  cursor: pointer;
  will-change: transform, opacity, box-shadow;
  transition: box-shadow 280ms cubic-bezier(.4, 0, .2, 1), opacity 15ms linear 30ms, transform 50ms cubic-bezier(0, 0, .1, 1) 0ms;
  user-select: none;

  &.active {
    box-shadow: rgb(169 169 169) 3px 3px 6px 0px inset, rgba(255, 255, 255, 0.5) -3px -3px 6px 1px inset;
    font-weight: 600;
    color: #2a2b39;

    .button__icon {
      box-shadow: rgb(169, 169, 169) -1px 4px 4px 0px inset, rgba(255, 255, 255, 0.5) -3px -3px 6px 1px inset;
    }
  }

  &:not(.active) {
    &:hover {
      box-shadow: rgb(37 44 97 / 20%) 0 8px 22px 0, rgba(93, 100, 148, 0.2) 0 4px 6px 0;
    }
    &:active {
      box-shadow: rgb(62 63 71 / 41%) 0 8px 22px 0, rgba(93, 100, 148, 0.2) 0 4px 6px 0;
      transform: translateX(0.1em) translateY(0.1em);
    }
  }
  &:disabled {
    cursor: not-allowed;
    opacity: .5;
  }

  .button__icon, .button__text {
    display: flex;
    align-items: center;
    height: 100%;
  }

  .button__text {
    border-right: 1px solid #dedede;
    padding: 0 12px;
  }
  .button__icon {
    background: #fff;
    padding: 0 12px;
    font-size: 18px;
  }
}

.fixed-columns {
  position: sticky;
  left: 0;
  z-index: 3;
}

.fixed-header {
  display: flex;
  position: sticky;
  top: 0;
  z-index: 4;
  font-weight: 600;
  background-color: $backgroundColor;
}

.fixed-body {
  display: flex;
  flex-direction: column;
  background: $backgroundColor;
}

.fixed-row {
  display: flex;
}

.fixed-column {
  width: 150px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 40px;
}

.scrollable-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.scrollable-header {
  display: flex;
  position: sticky;
  top: 0;
  z-index: 2;
  font-weight: 600;
  will-change: transform;
  transition: transform 0.2s ease-out; /* Khớp với easing của Swiper */
}

.scrollable-section {
  width: 100%;
}

.scrollable-table {
  min-width: 100%;
}

.scrollable-row {
  display: flex;
}

.column-header,
.column-data {
  width: 200px;
  padding: 12px;
  border-bottom: 1px solid #e5e9f5;
  flex-shrink: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 40px;
  background-color: $backgroundColor;
}

.actions-column {
  // width: 200px;
  // padding: 12px;
  // border-left: 1px solid #ddd;
  // border-bottom: 1px solid #ddd;
  // text-align: center;
  // display: flex;
  // gap: 8px;
  // justify-content: center;
  // position: sticky;
  // right: 0;
  // background: $backgroundColor;
  // z-index: 1;
  // height: 40px;
}

.fixed-row.hover,
.scrollable-row.hover {
  .fixed-column,
  .column-header,
  .column-data {
    background: #e5e9f5;
  }
}

.list-layout-btn {
  display: flex;
  align-items: center;
  border: 1px solid #aeb3bf;
  border-radius: 0.6em;
  padding: 0.6em 1em 0.5em 1em;
  color: #2c3755;
  font-size: 0.9em;
  transition: border 0.3s ease-out, box-shadow 0.3s ease-out;
  border: 1px solid #C5C4D3;
  border-radius: 6px;
  cursor: pointer;
  text-align: left;
  position: relative;
  padding: 8px 25px 8px 10px;
  color: #313949;
  background: #fff;
  height: 34px;
  font-weight: 500;

  &:hover {
    background: #e1e5ed;
    border: 1px solid #9298a9;
    color: #1b2541;
  }

  .mat-icon {
    font-size: 20px;
  }
}




.scrollable-section.swiper {
  overflow: visible !important;
}

.swiper-wrapper {
  display: flex;
  width: 100%;
}

.swiper-slide {
  flex-shrink: 0;
  width: auto;
}
.swiper-scrollbar {
  position: sticky !important;
}

@media (max-width: 768px) {
  // Mobile: Filter panel overlay mode
  .list-container {
    position: relative;
  }

  .field-filters {
    // Override animation states cho mobile - sử dụng overlay
    position: absolute !important;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 20;

    // Mobile filter panel có backdrop
    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: -1;
    }
  }

  .table-wrapper {
    // Table wrapper luôn full width trên mobile
    width: 100% !important;
  }

  .fixed-column {
    width: 100px;
  }

  .column-header,
  .column-data {
    width: 150px;
  }

  .actions-column {
    width: 80px;
    position: relative;

    button {
      display: none;
    }

    &::after {
      content: '⋮';
      font-size: 20px;
      cursor: pointer;
    }

    &:hover .actions-dropdown {
      display: block;
    }
  }

  .actions-dropdown {
    display: none;
    position: absolute;
    top: 40px;
    right: 0;
    background: $backgroundColor;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    z-index: 10;

    button {
      display: block;
      width: 100%;
      text-align: left;
      margin-bottom: 4px;
    }
  }
}
